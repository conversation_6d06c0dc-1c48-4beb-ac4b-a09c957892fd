import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:wiggyz_app/features/tournament_provider.dart';
import 'package:wiggyz_app/features/tournament_models.dart';
import 'package:wiggyz_app/screens/tournament_details_screen_new.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/core/utils/permissions.dart';
import 'package:wiggyz_app/utils/match_utils.dart';

class GamesScreen extends StatefulWidget {
  final int initialTabIndex;
  const GamesScreen({
    super.key,
    this.initialTabIndex = 1, // Changed default to 1 (Matches tab)
  });

  @override
  State<GamesScreen> createState() => _GamesScreenState();
}

class _GamesScreenState extends State<GamesScreen>
    with SingleTickerProviderStateMixin {
  String _searchQuery = "";
  late TabController _tabController;
  int _selectedFilterIndex = 0;

  // Add isDarkMode getter to avoid repeated theme checks
  bool get isDarkMode => Theme.of(context).brightness == Brightness.dark;
  @override
  void initState() {
    super.initState();

    _tabController = TabController(
      length: 2, // Changed from 3 to 2 - removed My Joined tab
      vsync: this,
      initialIndex: widget.initialTabIndex,
    );

    _tabController.addListener(() {
      if (mounted) setState(() {}); // rebuild to toggle FAB
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final provider = Provider.of<TournamentProvider>(
          context,
          listen: false,
        );

        provider.fetchTournaments();
        provider.fetchAllMatches(); // Fetch all matches for the matches tab
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        title: Text(
          'Gaming Zone',
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        automaticallyImplyLeading: false,
      ),
      floatingActionButton: _buildFloatingActionButton(authProvider),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Tab Bar
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      _tabController.animateTo(0);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color:
                            _tabController.index == 0
                                ? Colors.white.withOpacity(0.2)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.emoji_events,
                            color: Colors.black,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Tournaments',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      _tabController.animateTo(1);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color:
                            _tabController.index == 1
                                ? Colors.white.withOpacity(0.2)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.games,
                            color: Colors.black,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Matches',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ), // Main Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Tournaments Tab Content
                SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildSearchBar(),
                      const SizedBox(height: 12),
                      _buildFilterTabs(),
                      const SizedBox(height: 16),
                      _buildFeaturedTournamentsSection(context),
                    ],
                  ),
                ), // Matches Tab Content
                _buildMatchesTabContent(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Container(
            height: 48,
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[200],
              borderRadius: BorderRadius.circular(24),
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: "Search tournaments...",
                hintStyle: GoogleFonts.poppins(
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  fontSize: 14,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
            bottom: 8,
            top: 16,
          ),
          child: Text(
            "Game Categories",
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      height: 42,
      padding: const EdgeInsets.only(left: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterButton(0, "All Games"),
          _buildFilterButton(1, "Free Fire"),
          _buildFilterButton(2, "PUBG"),
          _buildFilterButton(3, "Fortnite"),
          _buildFilterButton(4, "Call of Duty"),
          _buildFilterButton(5, "League of Legends"),
          _buildFilterButton(6, "Valorant"),
          _buildFilterButton(7, "CS:GO"),
        ],
      ),
    );
  }

  Widget _buildFilterButton(int index, String label) {
    final isSelected = _selectedFilterIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilterIndex = index;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 12, bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? const Color(0xFFFFCC00)
                  : (isDarkMode
                      ? const Color(0xFF2A2A2A)
                      : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFFFFCC00) : Colors.transparent,
            width: 1.5,
          ),
          boxShadow:
              isSelected
                  ? [
                    BoxShadow(
                      color: const Color(0xFFFFCC00).withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
        ),
        child: Center(
          child: Text(
            label,
            style: GoogleFonts.poppins(
              color:
                  isSelected
                      ? Colors.black
                      : (isDarkMode ? Colors.white : Colors.black87),
              fontSize: 13,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  String _getFilterName(int index) {
    switch (index) {
      case 1:
        return "Free Fire";
      case 2:
        return "PUBG";
      case 3:
        return "Fortnite";
      case 4:
        return "Call of Duty";
      case 5:
        return "League of Legends";
      case 6:
        return "Valorant";
      case 7:
        return "CS:GO";
      default:
        return "";
    }
  }

  Widget _buildFeaturedTournamentsSection(BuildContext context) {
    return Consumer<TournamentProvider>(
      builder: (context, tournamentProvider, child) {
        if (tournamentProvider.isLoadingTournaments) {
          return const Center(child: CircularProgressIndicator());
        }

        if (tournamentProvider.tournamentsError != null &&
            tournamentProvider.tournaments.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Error: ${tournamentProvider.tournamentsError}',
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
              ),
            ),
          );
        }

        if (tournamentProvider.tournaments.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'No tournaments available.',
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
              ),
            ),
          );
        } // Apply filters to sorted tournaments (Upcoming, Live, Completed max 5)
        List<Tournament> filteredTournaments =
            tournamentProvider.sortedTournaments.where((tournament) {
              bool matchesFilter = true;
              if (_selectedFilterIndex != 0) {
                final filterName = _getFilterName(_selectedFilterIndex);
                matchesFilter =
                    tournament.game?.name.toLowerCase() ==
                    filterName.toLowerCase();
              }

              bool matchesSearch = true;
              if (_searchQuery.isNotEmpty) {
                final searchLower = _searchQuery.toLowerCase();
                matchesSearch =
                    tournament.name.toLowerCase().contains(searchLower) ||
                    (tournament.game?.name.toLowerCase().contains(
                          searchLower,
                        ) ??
                        false);
              }
              return matchesFilter && matchesSearch;
            }).toList();

        if (filteredTournaments.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'No tournaments match your criteria.',
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
              ),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Featured Tournaments',
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(height: 16),
              ...filteredTournaments.map(
                (tournament) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: _buildTournamentInfoCard(tournament),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMatchesTabContent() {
    return Consumer<TournamentProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingAllMatches) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.allMatchesError != null) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error: ${provider.allMatchesError}',
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      provider.fetchAllMatches();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        final allMatches = provider.allMatches;

        // Apply game filter first
        List<Match> gameFilteredMatches =
            allMatches.where((match) {
              bool matchesFilter = true;
              if (_selectedFilterIndex != 0) {
                final filterName = _getFilterName(_selectedFilterIndex);
                matchesFilter =
                    match.game?.name.toLowerCase() == filterName.toLowerCase();
              }

              bool matchesSearch = true;
              if (_searchQuery.isNotEmpty) {
                final searchLower = _searchQuery.toLowerCase();
                matchesSearch =
                    (match.participant1?.username?.toLowerCase().contains(
                          searchLower,
                        ) ??
                        false) ||
                    (match.participant2?.username?.toLowerCase().contains(
                          searchLower,
                        ) ??
                        false) ||
                    (match.game?.name.toLowerCase().contains(searchLower) ??
                        false);
              }
              return matchesFilter && matchesSearch;
            }).toList();

        // Categorize matches by status
        final now = DateTime.now();

        // Helper function to determine match status
        String getMatchStatus(Match match) {
          if (match.status == 'completed') return 'completed';
          if (match.status == 'live' || match.status == 'in_progress') {
            return 'live';
          }

          if (match.matchTime != null) {
            final matchTime = match.matchTime!;
            final timeDiff = matchTime.difference(now);

            // If match is within 15 minutes of start time, consider it live
            if (timeDiff.inMinutes.abs() <= 15) return 'live';
            // If match time has passed but not marked completed, consider it live
            if (matchTime.isBefore(now)) return 'live';
          }

          return 'upcoming';
        }

        // Categorize matches
        final upcomingMatches =
            gameFilteredMatches
                .where((match) => getMatchStatus(match) == 'upcoming')
                .toList()
              ..sort((a, b) {
                // Sort upcoming by match time (earliest first)
                if (a.matchTime == null && b.matchTime == null) return 0;
                if (a.matchTime == null) return 1;
                if (b.matchTime == null) return -1;
                return a.matchTime!.compareTo(b.matchTime!);
              });

        final liveMatches =
            gameFilteredMatches
                .where((match) => getMatchStatus(match) == 'live')
                .toList()
              ..sort((a, b) {
                // Sort live by match time (most recent first)
                if (a.matchTime == null && b.matchTime == null) return 0;
                if (a.matchTime == null) return 1;
                if (b.matchTime == null) return -1;
                return b.matchTime!.compareTo(a.matchTime!);
              });

        final completedMatches =
            gameFilteredMatches
                .where((match) => getMatchStatus(match) == 'completed')
                .toList()
              ..sort((a, b) {
                // Sort completed by match time (most recent first)
                if (a.matchTime == null && b.matchTime == null) return 0;
                if (a.matchTime == null) return 1;
                if (b.matchTime == null) return -1;
                return b.matchTime!.compareTo(a.matchTime!);
              });

        // Take only the 5 most recent completed matches
        final recentCompletedMatches = completedMatches.take(5).toList();

        return Column(
          children: [
            // Search Bar for Matches Tab
            _buildSearchBar(),
            const SizedBox(height: 12),

            // Filter Tabs for Matches Tab
            _buildFilterTabs(),
            const SizedBox(height: 16),

            // Matches Section - Status-based categorization
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Upcoming Matches Section
                    if (upcomingMatches.isNotEmpty) ...[
                      _buildMatchSection(
                        'Upcoming Matches',
                        upcomingMatches,
                        isDarkMode,
                        iconData: Icons.access_time,
                        iconColor: Colors.orange,
                      ),
                      const SizedBox(height: 20),
                    ],

                    // Live Matches Section
                    if (liveMatches.isNotEmpty) ...[
                      _buildMatchSection(
                        'Live Matches',
                        liveMatches,
                        isDarkMode,
                        iconData: Icons.play_circle_filled,
                        iconColor: Colors.red,
                      ),
                      const SizedBox(height: 20),
                    ],

                    // Completed Matches Section
                    if (recentCompletedMatches.isNotEmpty) ...[
                      _buildMatchSection(
                        'Recent Completed (Last 5)',
                        recentCompletedMatches,
                        isDarkMode,
                        iconData: Icons.check_circle,
                        iconColor: Colors.green,
                      ),
                      const SizedBox(height: 20),
                    ],

                    // No matches message
                    if (upcomingMatches.isEmpty &&
                        liveMatches.isEmpty &&
                        recentCompletedMatches.isEmpty)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Column(
                            children: [
                              Icon(
                                Icons.sports_esports,
                                size: 64,
                                color:
                                    isDarkMode
                                        ? Colors.white30
                                        : Colors.black26,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No matches found',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      isDarkMode
                                          ? Colors.white70
                                          : Colors.black54,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Try adjusting your filters or create a new match',
                                style: TextStyle(
                                  fontSize: 14,
                                  color:
                                      isDarkMode
                                          ? Colors.white54
                                          : Colors.black38,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMatchSection(
    String title,
    List<Match> matches,
    bool isDarkMode, {
    IconData? iconData,
    Color? iconColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              if (iconData != null) ...[
                Icon(
                  iconData,
                  size: 20,
                  color:
                      iconColor ?? (isDarkMode ? Colors.white : Colors.black),
                ),
                const SizedBox(width: 8),
              ],
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color:
                      iconColor?.withOpacity(0.2) ??
                      Colors.grey.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${matches.length}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color:
                        iconColor ?? (isDarkMode ? Colors.white : Colors.black),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Matches List
          ...matches.map((match) => _buildMatchInfoCard(match)),
        ],
      ),
    );
  }

  Widget _buildMatchInfoCard(Match match) {
    // Helper function to determine match status for badge display
    String getMatchStatus(Match match) {
      if (match.status == 'completed') return 'completed';
      if (match.status == 'live' || match.status == 'in_progress') {
        return 'live';
      }

      if (match.matchTime != null) {
        final now = DateTime.now();
        final matchTime = match.matchTime!;
        final timeDiff = matchTime.difference(now);

        // If match is within 15 minutes of start time, consider it live
        if (timeDiff.inMinutes.abs() <= 15) return 'live';
        // If match time has passed but not marked completed, consider it live
        if (matchTime.isBefore(now)) return 'live';
      }

      return 'upcoming';
    }

    Widget buildStatusBadge(String status) {
      switch (status) {
        case 'completed':
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'COMPLETED',
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          );
        case 'live':
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  'LIVE',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          );
        default: // upcoming
          final timeRemaining = MatchUtils.getTimeRemainingBadge(
            match.matchTime,
          );
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              timeRemaining,
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          );
      }
    } // Safely handle match data

    final participant1Name = match.participant1?.username ?? 'TBD';
    final participant2Name = match.participant2?.username ?? 'TBD';
    final gameName = match.game?.name ?? 'Unknown Game';

    // Use match title instead of round number
    final matchTitle = MatchUtils.generateMatchTitle(
      match.title,
      gameName,
      match.id,
    );

    // Format time in local timezone
    final formattedDateTime = MatchUtils.formatLocalDateTime(match.matchTime);

    // Get participant count and calculate current and max prize pools
    final participantCount = MatchUtils.getParticipantCount(
      match.match_participants,
    );
    final prizePoolText = MatchUtils.formatCurrentAndMaxPrizePool(
      match.entryFee ?? 0.0,
      participantCount,
      match.maxParticipants ?? 10, // Use reasonable default instead of 0
    );

    final status = getMatchStatus(match);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        border: Border.all(color: const Color(0xFFFFECB3), width: 1),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row with game type and status badge
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      gameName,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color:
                            isDarkMode
                                ? Colors.grey[400]
                                : Colors.black.withOpacity(0.7),
                      ),
                    ),
                    buildStatusBadge(status),
                  ],
                ),
                const SizedBox(height: 8),

                // Match title
                Text(
                  matchTitle,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 12),

                // Players row
                Row(
                  children: [
                    const Icon(Icons.person, size: 16, color: Colors.grey),
                    const SizedBox(width: 6),
                    Text(
                      '$participant1Name vs $participant2Name',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[300] : Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Entry fee and prize pool row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Row(
                        children: [
                          const Icon(
                            Icons.monetization_on,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 6),
                          Flexible(
                            child: Text(
                              match.entryFee != null
                                  ? '₹${match.entryFee!.toStringAsFixed(0)} Entry'
                                  : 'Free Entry',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color:
                                    isDarkMode
                                        ? Colors.grey[300]
                                        : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Flexible(
                      child: Row(
                        children: [
                          const Icon(
                            Icons.emoji_events,
                            size: 16,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 6),
                          Flexible(
                            child: Text(
                              '$prizePoolText Prize',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color:
                                    isDarkMode
                                        ? Colors.grey[300]
                                        : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Date time row
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 16, color: Colors.grey),
                    const SizedBox(width: 6),
                    Flexible(
                      child: Text(
                        formattedDateTime,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: isDarkMode ? Colors.grey[300] : Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Divider(height: 1, thickness: 1, color: Color(0xFFEEEEEE)),
          ),
          GestureDetector(
            onTap: () {
              // Navigate to Match Details Screen
              context.go('/matches/${match.id}');
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: const BoxDecoration(
                color: Color(0xFFFFA500),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Center(
                child: Text(
                  "View Details",
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTournamentInfoCard(Tournament tournament) {
    // Safely format dates with error handling
    String dateTimeString;
    try {
      String formattedDate = DateFormat(
        'MMM d, yyyy',
      ).format(tournament.startDate);
      String formattedTime = DateFormat('h:mm a').format(tournament.startDate);
      dateTimeString = "$formattedDate, $formattedTime";
    } catch (e) {
      dateTimeString = 'Date TBD';
    }

    // Safely handle tournament name and other properties
    String tournamentName =
        tournament.name.isNotEmpty ? tournament.name : 'Unnamed Tournament';
    String gameName = tournament.game?.name ?? 'Unknown Game';

    // Determine tournament status
    final now = DateTime.now();
    String status;
    Color statusColor;
    Color statusBackgroundColor;

    if (now.isBefore(tournament.startDate)) {
      status = 'Upcoming';
      statusColor = Colors.blue;
      statusBackgroundColor = Colors.blue.withOpacity(0.1);
    } else if (now.isAfter(tournament.startDate) &&
        now.isBefore(tournament.endDate)) {
      status = 'Live';
      statusColor = Colors.green;
      statusBackgroundColor = Colors.green.withOpacity(0.1);
    } else {
      status = 'Completed';
      statusColor = Colors.grey;
      statusBackgroundColor = Colors.grey.withOpacity(0.1);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        border: Border.all(color: const Color(0xFFFFECB3), width: 1),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      gameName,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color:
                            isDarkMode
                                ? Colors.grey[400]
                                : Colors.black.withOpacity(0.7),
                      ),
                    ),
                    // Status indicator
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: statusBackgroundColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: statusColor, width: 1),
                      ),
                      child: Text(
                        status,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  tournamentName,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.people, size: 16, color: Colors.grey),
                    const SizedBox(width: 6),
                    Text(
                      '${tournament.currentParticipants}/${tournament.maxParticipants} registered',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[300] : Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.monetization_on,
                      size: 16,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '₹${tournament.entryFee} Entry Fee',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[300] : Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 16, color: Colors.grey),
                    const SizedBox(width: 6),
                    Text(
                      dateTimeString,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[300] : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Divider(height: 1, thickness: 1, color: Color(0xFFEEEEEE)),
          ),
          GestureDetector(
            onTap: () {
              // Navigate to Tournament Details Screen
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => TournamentDetailsScreenNew(
                        tournamentId: tournament.id,
                      ),
                ),
              );
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: const BoxDecoration(
                color: Color(0xFFFFA500),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Center(
                child: Text(
                  "View Details",
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build floating action button based on current tab and user permissions
  Widget? _buildFloatingActionButton(AuthProvider authProvider) {
    if (_tabController.index == 0) {
      // Tournaments tab - show create tournament button if user has permission
      if (PermissionUtils.canUserCreateTournament(authProvider.userData)) {
        return FloatingActionButton(
          onPressed: () {
            context.go('/tournaments/create');
          },
          backgroundColor: const Color(0xFFFFA500),
          tooltip: 'Create Tournament',
          child: const Icon(Icons.add),
        );
      }
    } else if (_tabController.index == 1) {
      // Matches tab - show create match button (all users can create matches)
      return FloatingActionButton(
        onPressed: () {
          context.go('/matches/create');
        },
        backgroundColor: const Color(0xFFFFA500),
        tooltip: 'Create Match',
        child: const Icon(Icons.add),
      );
    }
    return null; // No button for unauthorized users or other tabs
  }
}
